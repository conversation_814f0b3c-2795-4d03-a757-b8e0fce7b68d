"use client"

import { useEffect, useState } from "react"

export default function SyntheticV0PageForDeployment() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p>Loading FlexLiving Reviews Dashboard...</p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', fontFamily: 'Inter, sans-serif' }}>
      <nav style={{
        background: '#fff',
        borderBottom: '1px solid #e5e7eb',
        padding: '1rem 2rem',
        display: 'flex',
        alignItems: 'center',
        gap: '1rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <div style={{
            width: '32px',
            height: '32px',
            background: '#323927',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold'
          }}>
            FL
          </div>
          <span style={{ fontSize: '1.25rem', fontWeight: '600' }}>
            FlexLiving <span style={{ color: '#D4F872' }}>Reviews</span>
          </span>
        </div>
      </nav>
      <main style={{ padding: '2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={{ marginBottom: '2rem' }}>
            <h1 style={{ fontSize: '2.5rem', fontWeight: '700', marginBottom: '0.5rem', color: '#323927' }}>
              FlexLiving Reviews Dashboard
            </h1>
            <p style={{ color: '#93968B', fontSize: '1.125rem' }}>
              Manage and analyze guest reviews across all properties
            </p>
          </div>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem',
            marginBottom: '2rem'
          }}>
            <div style={{
              background: '#fff',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
              border: '1px solid #e5e7eb'
            }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
                Total Reviews
              </h3>
              <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>1,247</p>
              <p style={{ fontSize: '0.875rem', color: '#93968B' }}>+12% from last month</p>
            </div>

            <div style={{
              background: '#fff',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
              border: '1px solid #e5e7eb'
            }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
                Average Rating
              </h3>
              <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>4.8</p>
              <p style={{ fontSize: '0.875rem', color: '#93968B' }}>⭐⭐⭐⭐⭐</p>
            </div>

            <div style={{
              background: '#fff',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
              border: '1px solid #e5e7eb'
            }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
                Active Properties
              </h3>
              <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>24</p>
              <p style={{ fontSize: '0.875rem', color: '#93968B' }}>Across 3 cities</p>
            </div>

            <div style={{
              background: '#fff',
              borderRadius: '12px',
              padding: '1.5rem',
              boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
              border: '1px solid #e5e7eb'
            }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
                Response Rate
              </h3>
              <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>98%</p>
              <p style={{ fontSize: '0.875rem', color: '#93968B' }}>Within 24 hours</p>
            </div>
          </div>

          <div style={{
            background: '#fff',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#323927' }}>
                Recent Reviews
              </h2>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                <button style={{
                  background: '#D4F872',
                  color: '#323927',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  Sync Data
                </button>
                <button style={{
                  background: '#fff',
                  color: '#323927',
                  border: '1px solid #CECEC7',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  Export CSV
                </button>
              </div>
            </div>

            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem'
            }}>
              {[
                { guest: 'Sarah Johnson', property: 'Downtown Loft', rating: 5, date: '2024-01-15', review: 'Amazing stay! The location was perfect and the apartment was spotless.' },
                { guest: 'Mike Chen', property: 'Riverside Studio', rating: 4, date: '2024-01-14', review: 'Great value for money. Clean and comfortable with excellent amenities.' },
                { guest: 'Emma Wilson', property: 'City Center Suite', rating: 5, date: '2024-01-13', review: 'Exceeded expectations! Will definitely book again.' }
              ].map((review, index) => (
                <div key={index} style={{
                  padding: '1rem',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  background: '#FBFAF9'
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <div>
                      <h4 style={{ fontSize: '1rem', fontWeight: '600', color: '#323927', marginBottom: '0.25rem' }}>
                        {review.guest}
                      </h4>
                      <p style={{ fontSize: '0.875rem', color: '#93968B' }}>
                        {review.property} • {review.date}
                      </p>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <span style={{ fontSize: '1rem', fontWeight: '600', color: '#323927' }}>
                        {review.rating}
                      </span>
                      <span style={{ color: '#fbbf24' }}>
                        {'⭐'.repeat(review.rating)}
                      </span>
                    </div>
                  </div>
                  <p style={{ fontSize: '0.875rem', color: '#323927', lineHeight: '1.5' }}>
                    {review.review}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}