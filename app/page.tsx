"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"

// Dynamically import components to avoid SSR issues
const HostawayDashboard = dynamic(() => import("../components/HostawayDashboard"), {
  ssr: false,
  loading: () => <DashboardLoading />
})

const GooglePlacesDashboard = dynamic(() => import("../frontend/src/components/GooglePlaces/GooglePlaces"), {
  ssr: false,
  loading: () => <DashboardLoading />
})

function DashboardLoading() {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '50vh',
      flexDirection: 'column',
      gap: '1rem'
    }}>
      <div style={{
        width: '40px',
        height: '40px',
        border: '4px solid #f3f3f3',
        borderTop: '4px solid #323927',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}></div>
      <p>Loading dashboard...</p>
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}

export default function SyntheticV0PageForDeployment() {
  const [mounted, setMounted] = useState(false)
  const [activeTab, setActiveTab] = useState<'hostaway' | 'google'>('hostaway')

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #323927',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p>Loading FlexLiving Reviews Dashboard...</p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', fontFamily: 'Inter, sans-serif' }}>
      <nav style={{
        background: '#fff',
        borderBottom: '1px solid #e5e7eb',
        padding: '1rem 2rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <div style={{
            width: '32px',
            height: '32px',
            background: '#323927',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold'
          }}>
            FL
          </div>
          <span style={{ fontSize: '1.25rem', fontWeight: '600' }}>
            FlexLiving <span style={{ color: '#D4F872' }}>Reviews</span>
          </span>
        </div>

        {/* Tab Navigation */}
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <button
            onClick={() => setActiveTab('hostaway')}
            style={{
              background: activeTab === 'hostaway' ? '#323927' : 'transparent',
              color: activeTab === 'hostaway' ? 'white' : '#323927',
              border: `1px solid ${activeTab === 'hostaway' ? '#323927' : '#e5e7eb'}`,
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            📊 Hostaway Reviews
          </button>
          <button
            onClick={() => setActiveTab('google')}
            style={{
              background: activeTab === 'google' ? '#323927' : 'transparent',
              color: activeTab === 'google' ? 'white' : '#323927',
              border: `1px solid ${activeTab === 'google' ? '#323927' : '#e5e7eb'}`,
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            🌍 Google Places
          </button>
        </div>
      </nav>

      <main style={{ padding: '0' }}>
        {activeTab === 'hostaway' && <HostawayDashboard />}
        {activeTab === 'google' && <GooglePlacesDashboard />}
      </main>
    </div>
  )
}