"use client"

import { useEffect, useState } from "react"
import { reviewsApi, propertiesApi, hostawayApi, type Review, type Property, type ReviewStatistics } from "../frontend/src/services/api"

interface DashboardData {
  reviews: Review[]
  properties: Property[]
  statistics: ReviewStatistics | null
}

export default function HostawayDashboard() {
  const [data, setData] = useState<DashboardData>({
    reviews: [],
    properties: [],
    statistics: null
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [syncing, setSyncing] = useState(false)

  const loadData = async () => {
    try {
      setError(null)
      const [reviewsData, propertiesData, statisticsData] = await Promise.all([
        reviewsApi.getReviews({ limit: 10 }),
        propertiesApi.getProperties(),
        reviewsApi.getStatistics()
      ])

      setData({
        reviews: reviewsData,
        properties: propertiesData,
        statistics: statisticsData
      })
    } catch (err) {
      console.error('Error loading dashboard data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const handleSync = async () => {
    setSyncing(true)
    try {
      await hostawayApi.syncData()
      await loadData() // Reload data after sync
    } catch (err) {
      console.error('Error syncing data:', err)
      setError(err instanceof Error ? err.message : 'Failed to sync data')
    } finally {
      setSyncing(false)
    }
  }

  const handleExport = async () => {
    try {
      const blob = await reviewsApi.exportReviews('csv')
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `reviews-export-${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Error exporting reviews:', err)
      setError(err instanceof Error ? err.message : 'Failed to export reviews')
    }
  }

  useEffect(() => {
    loadData()
  }, [])

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #323927',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}></div>
        <p>Loading Hostaway dashboard...</p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <div style={{
          background: '#fee2e2',
          border: '1px solid #fecaca',
          borderRadius: '8px',
          padding: '1rem',
          color: '#991b1b',
          marginBottom: '1rem'
        }}>
          <h3 style={{ margin: '0 0 0.5rem 0', fontWeight: '600' }}>Error Loading Dashboard</h3>
          <p style={{ margin: 0 }}>{error}</p>
        </div>
        <button
          onClick={loadData}
          style={{
            background: '#323927',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '0.5rem 1rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    )
  }

  const stats = data.statistics?.overview || {
    avgRating: 0,
    totalReviews: 0,
    publishedReviews: 0,
    websiteReviews: 0
  }

  return (
    <div style={{ padding: '2rem' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ marginBottom: '2rem' }}>
          <h1 style={{ fontSize: '2.5rem', fontWeight: '700', marginBottom: '0.5rem', color: '#323927' }}>
            Hostaway Reviews Dashboard
          </h1>
          <p style={{ color: '#93968B', fontSize: '1.125rem' }}>
            Manage and analyze guest reviews from Hostaway properties
          </p>
        </div>
        
        {/* Statistics Cards */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
              Total Reviews
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>{stats.totalReviews}</p>
            <p style={{ fontSize: '0.875rem', color: '#93968B' }}>Published: {stats.publishedReviews}</p>
          </div>
          
          <div style={{
            background: '#fff',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
              Average Rating
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>
              {stats.avgRating ? stats.avgRating.toFixed(1) : '0.0'}
            </p>
            <p style={{ fontSize: '0.875rem', color: '#93968B' }}>
              {'⭐'.repeat(Math.round(stats.avgRating || 0))}
            </p>
          </div>
          
          <div style={{
            background: '#fff',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
              Active Properties
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>
              {data.properties.filter(p => p.isActive).length}
            </p>
            <p style={{ fontSize: '0.875rem', color: '#93968B' }}>Total: {data.properties.length}</p>
          </div>
          
          <div style={{
            background: '#fff',
            borderRadius: '12px',
            padding: '1.5rem',
            boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            border: '1px solid #e5e7eb'
          }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem', color: '#323927' }}>
              Website Reviews
            </h3>
            <p style={{ fontSize: '2rem', fontWeight: '700', color: '#323927' }}>{stats.websiteReviews}</p>
            <p style={{ fontSize: '0.875rem', color: '#93968B' }}>
              {stats.totalReviews > 0 ? Math.round((stats.websiteReviews / stats.totalReviews) * 100) : 0}% of total
            </p>
          </div>
        </div>

        {/* Recent Reviews */}
        <div style={{
          background: '#fff',
          borderRadius: '12px',
          padding: '1.5rem',
          boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '1.5rem'
          }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#323927' }}>
              Recent Reviews
            </h2>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button 
                onClick={handleSync}
                disabled={syncing}
                style={{
                  background: syncing ? '#93968B' : '#D4F872',
                  color: '#323927',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: syncing ? 'not-allowed' : 'pointer'
                }}
              >
                {syncing ? 'Syncing...' : 'Sync Data'}
              </button>
              <button 
                onClick={handleExport}
                style={{
                  background: '#fff',
                  color: '#323927',
                  border: '1px solid #CECEC7',
                  borderRadius: '8px',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Export CSV
              </button>
            </div>
          </div>
          
          {data.reviews.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#93968B' }}>
              <p>No reviews found. Try syncing data from Hostaway.</p>
            </div>
          ) : (
            <div style={{ 
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem'
            }}>
              {data.reviews.slice(0, 5).map((review) => (
                <div key={review._id} style={{
                  padding: '1rem',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  background: '#FBFAF9'
                }}>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'flex-start',
                    marginBottom: '0.5rem'
                  }}>
                    <div>
                      <h4 style={{ fontSize: '1rem', fontWeight: '600', color: '#323927', marginBottom: '0.25rem' }}>
                        {review.guestName || 'Anonymous Guest'}
                      </h4>
                      <p style={{ fontSize: '0.875rem', color: '#93968B' }}>
                        {review.listingName} • {new Date(review.submittedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <span style={{ fontSize: '1rem', fontWeight: '600', color: '#323927' }}>
                        {review.rating}/10
                      </span>
                      <span style={{ color: '#fbbf24' }}>
                        {'⭐'.repeat(Math.round(review.rating / 2))}
                      </span>
                    </div>
                  </div>
                  <p style={{ fontSize: '0.875rem', color: '#323927', lineHeight: '1.5' }}>
                    {review.publicReview || 'No review text available'}
                  </p>
                  <div style={{ marginTop: '0.5rem', fontSize: '0.75rem', color: '#93968B' }}>
                    Channel: {review.channel} • Status: {review.status}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
