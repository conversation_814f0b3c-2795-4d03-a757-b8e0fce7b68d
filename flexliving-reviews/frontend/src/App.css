.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-primary);
}

.app-nav {
  background: linear-gradient(135deg, #2d4a3e 0%, #323927 100%);
  border-bottom: none;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(45, 74, 62, 0.15);
}

.nav-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 700;
  text-decoration: none;
  color: var(--color-white);
  transition: all var(--transition-fast);
}

.nav-logo:hover {
  transform: translateY(-1px);
  color: var(--color-white);
}

.logo-image {
  height: 36px;
  width: auto;
  object-fit: contain;
  border-radius: var(--radius-sm);
}

.logo-text {
  color: var(--color-white);
  font-weight: 700;
  font-size: 1.25rem;
}

.logo-accent {
  color: var(--color-accent);
  font-weight: 500;
  font-size: 1rem;
  margin-left: -0.25rem;
}

/* Enhanced nav links to accommodate Google Places link */
.nav-links {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
  white-space: nowrap;
}

.nav-link:hover {
  color: var(--color-white);
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.nav-link:hover::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: var(--color-accent);
  border-radius: 50%;
}

.nav-settings {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--radius-md);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.nav-settings:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-white);
  transform: translateY(-1px);
}

.app-main {
  flex: 1;
  background: var(--color-bg-primary);
  min-height: calc(100vh - 80px);
}

/* Enhanced responsive design for additional navigation items */
@media (max-width: 768px) {
  .nav-container {
    padding: 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-link span {
    display: none;
  }

  .nav-link {
    padding: 0.5rem;
    min-width: 40px;
    justify-content: center;
  }

  .logo-text {
    font-size: 1rem;
  }

  .logo-accent {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0.75rem;
  }

  .nav-links {
    gap: 0.5rem;
  }

  .logo-text {
    display: none;
  }

  .logo-accent {
    margin-left: 0;
  }

  .nav-link {
    padding: 0.375rem;
    min-width: 36px;
  }

  .nav-settings {
    width: 36px;
    height: 36px;
  }
}
