import { BrowserRouter as Router, Routes, Route, Link } from "react-router-dom"
import Dashboard from "./components/Dashboard/Dashboard"
import GooglePlaces from "./components/GooglePlaces/GooglePlaces"
import PublicReviews from "./components/PublicReviews/PublicReviews"
import PropertyDetail from "./components/PropertyDetail/PropertyDetail"
import "./styles/globals.css"
import "./App.css"
import { BarChart3, Building2, Settings, Globe } from "lucide-react"

function App() {
  return (
    <Router>
      <div className="app">
        <nav className="app-nav">
          <div className="nav-container">
            <Link to="/" className="nav-logo">
              <img src="/flex.webp" alt="FlexLiving" className="logo-image" />
              <span className="logo-text">FlexLiving</span>
              <span className="logo-accent">Reviews</span>
            </Link>
            <div className="nav-links">
              <Link to="/" className="nav-link">
                <BarChart3 size={18} />
                <span>Hostaway Reviews</span>
              </Link>
              <Link to="/google-places" className="nav-link">
                <Globe size={18} />
                <span>Google Places</span>
              </Link>
              <Link to="/property/prop_001" className="nav-link">
                <Building2 size={18} />
                <span>Sample Property</span>
              </Link>
              <button className="nav-settings">
                <Settings size={18} />
              </button>
            </div>
          </div>
        </nav>

        <main className="app-main">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/google-places" element={<GooglePlaces />} />
            <Route path="/property/:propertyId" element={<PropertyDetail />} />
            <Route path="/property/:propertyId/public" element={<PublicReviews />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
