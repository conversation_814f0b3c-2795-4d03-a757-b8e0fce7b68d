.dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-bg-primary) 0%, var(--color-bg-secondary) 100%);
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1.5rem;
}

.dashboard-loading p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

.dashboard-header {
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-bg-secondary) 100%);
  border-bottom: 1px solid var(--color-bg-tertiary);
  padding: 3rem 0 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-accent), var(--color-primary));
}

/* Enhanced header content layout to accommodate integration card */
.dashboard-header .header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.header-title-section {
  flex: 1;
}

.dashboard-header h1 {
  color: var(--color-primary);
  margin-bottom: 0.75rem;
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.dashboard-header .subtitle {
  color: var(--color-text-secondary);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

/* Added integration card for Google Places */
.integration-card {
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-accent-light) 100%);
  border: 2px solid var(--color-accent);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  min-width: 300px;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.integration-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.integration-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.integration-content svg {
  color: var(--color-primary);
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.integration-text h3 {
  color: var(--color-primary);
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.integration-text p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.integration-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--color-primary);
  color: var(--color-white);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.integration-link:hover {
  background: var(--color-primary-dark);
  color: var(--color-white);
  transform: translateX(2px);
}

.header-actions {
  display: flex;
  gap: 1rem;
  max-width: 1280px;
  margin: 2rem auto 0;
  padding: 0 2rem;
}

.header-actions button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.header-actions button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.header-actions button:hover::before {
  left: 100%;
}

.header-actions button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.dashboard-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
}

.content-header {
  margin-bottom: 2rem;
}

.tabs {
  display: flex;
  gap: 0.5rem;
  background: var(--color-white);
  padding: 0.5rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-bg-secondary);
  position: relative;
}

.tab {
  padding: 0.875rem 1.75rem;
  background: transparent;
  border: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  position: relative;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab:hover {
  color: var(--color-text-primary);
  background: var(--color-bg-secondary);
}

.tab.active {
  color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-accent-light), var(--color-accent));
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

.tab.active::after {
  display: none;
}

.reviews-section,
.properties-section,
.analytics-section {
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced responsive design for integration card */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 2rem 0 1.5rem;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .dashboard-header .header-content {
    flex-direction: column;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  .integration-card {
    min-width: auto;
    width: 100%;
  }

  .header-actions,
  .dashboard-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .header-actions button {
    width: 100%;
    justify-content: center;
  }

  .tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0.375rem;
  }

  .tab {
    white-space: nowrap;
    font-size: 0.8125rem;
    padding: 0.75rem 1.25rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header h1 {
    font-size: 1.75rem;
  }

  .dashboard-header .subtitle {
    font-size: 0.875rem;
  }

  .header-actions button {
    padding: 0.75rem 1.25rem;
    font-size: 0.8125rem;
  }

  .integration-content {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .integration-text h3 {
    font-size: 1rem;
  }
}
