.google-places {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-bg-primary) 0%, var(--color-bg-secondary) 100%);
}

/* Modern header design matching the enhanced Dashboard */
.google-places-header {
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-bg-secondary) 100%);
  border-bottom: 1px solid var(--color-bg-tertiary);
  padding: 3rem 0 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.google-places-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-accent), var(--color-primary));
}

.header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-title {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.header-title svg {
  color: var(--color-primary);
  margin-top: 0.25rem;
}

.header-title h1 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.subtitle {
  color: var(--color-text-secondary);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

/* API status indicator */
.api-status {
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
}

.api-status.configured {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
  border: 1px solid #86efac;
}

.api-status.not-configured {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
  border: 1px solid #fcd34d;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.google-places-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
}

/* Enhanced search section */
.search-section {
  margin-bottom: 2rem;
}

.search-container {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-bg-secondary);
}

.search-input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  transition: all var(--transition-fast);
}

.search-input-group:focus-within {
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(212, 248, 114, 0.1);
}

.search-input-group svg {
  color: var(--color-text-secondary);
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: var(--color-text-primary);
  outline: none;
  font-family: var(--font-family);
}

.search-input::placeholder {
  color: var(--color-text-muted);
}

.search-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--color-accent), var(--color-accent-hover));
  color: var(--color-primary);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Error message styling */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #991b1b;
  border: 1px solid #fca5a5;
  border-radius: var(--radius-lg);
  margin-top: 1rem;
  font-size: 0.875rem;
}

/* Search results grid */
.search-results {
  margin-bottom: 2rem;
}

.search-results h3 {
  color: var(--color-text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.places-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

.place-card {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-bg-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.place-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-accent), var(--color-primary));
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.place-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.place-card:hover::before {
  opacity: 1;
}

.place-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.place-header h4 {
  color: var(--color-text-primary);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
}

.place-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-primary);
  font-weight: 500;
  flex-shrink: 0;
}

.place-rating svg {
  color: var(--color-accent);
}

.rating-count {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  font-weight: 400;
}

.place-address {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.place-reviews-count {
  color: var(--color-text-muted);
  font-size: 0.8125rem;
  font-style: italic;
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  gap: 1rem;
}

.loading-container svg {
  color: var(--color-accent);
}

.loading-container p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

/* Place details section */
.place-details {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-bg-secondary);
  overflow: hidden;
  position: relative;
}

.place-details::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-accent), var(--color-primary));
}

.place-details-header {
  padding: 2rem;
  border-bottom: 1px solid var(--color-bg-secondary);
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-bg-secondary) 100%);
}

.place-info h2 {
  color: var(--color-primary);
  margin-bottom: 1rem;
  font-size: 1.75rem;
  font-weight: 700;
}

.place-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.place-address,
.place-rating-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
}

.place-rating-summary svg {
  color: var(--color-accent);
}

.reviews-section {
  padding: 2rem;
}

.reviews-section h3 {
  color: var(--color-text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.no-reviews {
  text-align: center;
  padding: 2rem;
  color: var(--color-text-secondary);
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Review card styling */
.review-card {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--color-bg-tertiary);
  transition: all var(--transition-fast);
}

.review-card:hover {
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.reviewer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.reviewer-name {
  font-weight: 600;
  color: var(--color-text-primary);
}

.review-date {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.review-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.875rem;
}

.review-rating.rating-excellent {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
}

.review-rating.rating-good {
  background: linear-gradient(135deg, var(--color-accent-light), var(--color-accent));
  color: var(--color-primary);
}

.review-rating.rating-average {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  color: #92400e;
}

.review-rating.rating-poor {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
  color: #991b1b;
}

.review-content {
  margin-bottom: 1rem;
}

.review-content p {
  color: var(--color-text-primary);
  line-height: 1.6;
  margin: 0;
}

.review-footer {
  display: flex;
  justify-content: flex-end;
}

.review-source {
  font-size: 0.75rem;
  color: var(--color-text-muted);
  font-style: italic;
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.empty-state svg {
  color: var(--color-text-muted);
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  color: var(--color-text-primary);
  margin-bottom: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-state > p {
  color: var(--color-text-secondary);
  margin-bottom: 2rem;
  font-size: 1rem;
}

.example-searches {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.example-searches p {
  color: var(--color-text-primary);
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.example-searches ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.example-searches li {
  color: var(--color-text-secondary);
  padding: 0.25rem 0;
  font-size: 0.875rem;
}

.example-searches li::before {
  content: "•";
  color: var(--color-accent);
  font-weight: bold;
  margin-right: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .google-places-header {
    padding: 2rem 0 1.5rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
  }

  .header-title h1 {
    font-size: 2rem;
  }

  .google-places-content {
    padding: 0 1rem 2rem;
  }

  .search-container {
    padding: 1.5rem;
  }

  .search-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .places-grid {
    grid-template-columns: 1fr;
  }

  .place-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .place-details-header {
    padding: 1.5rem;
  }

  .place-info h2 {
    font-size: 1.5rem;
  }

  .reviews-section {
    padding: 1.5rem;
  }

  .review-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .header-title {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .header-title svg {
    align-self: center;
  }

  .search-input {
    font-size: 0.875rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .example-searches {
    max-width: none;
  }
}
