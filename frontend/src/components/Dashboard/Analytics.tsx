"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
// Chart components will be implemented with simple HTML/CSS for now
import { 
  TrendingUp, 
  TrendingDown, 
  Star, 
  Calendar, 
  Users,
  Globe,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from "lucide-react"
import { type Review, type ReviewStatistics } from "../../services/api"

interface AnalyticsProps {
  statistics: ReviewStatistics | null
  reviews: Review[]
  selectedProperty: string
}

const Analytics: React.FC<AnalyticsProps> = ({ statistics, reviews, selectedProperty }) => {
  if (!statistics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Analytics & Insights</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No analytics data</h3>
            <p className="mt-1 text-sm text-gray-500">
              Analytics will appear here once you have review data.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const { overview, categoryBreakdown, channelBreakdown, monthlyTrend } = statistics

  // Prepare chart data
  const categoryChartData = categoryBreakdown.map(item => ({
    name: item._id,
    rating: item.avgRating,
    count: item.count,
  }))

  const channelChartData = channelBreakdown.map(item => ({
    name: item._id,
    count: item.count,
    rating: item.avgRating,
  }))

  const monthlyChartData = monthlyTrend.map(item => ({
    month: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
    count: item.count,
    rating: item.avgRating,
  })).sort((a, b) => a.month.localeCompare(b.month))

  // Rating distribution
  const ratingDistribution = reviews.reduce((acc, review) => {
    const ratingRange = Math.floor(review.rating / 2) * 2
    const key = `${ratingRange}-${ratingRange + 2}`
    acc[key] = (acc[key] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const ratingChartData = Object.entries(ratingDistribution).map(([range, count]) => ({
    range,
    count,
    percentage: (count / reviews.length) * 100
  }))

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.avgRating.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +0.2 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalReviews}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published Rate</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {overview.totalReviews > 0 ? Math.round((overview.publishedReviews / overview.totalReviews) * 100) : 0}%
            </div>
            <Progress 
              value={overview.totalReviews > 0 ? (overview.publishedReviews / overview.totalReviews) * 100 : 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Website Reviews</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.websiteReviews}</div>
            <p className="text-xs text-muted-foreground">
              {overview.totalReviews > 0 ? Math.round((overview.websiteReviews / overview.totalReviews) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="ratings">Rating Distribution</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Review Trends</CardTitle>
              <CardDescription>Review count and average rating over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyChartData.map((item, index) => (
                  <div key={item.month} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="font-medium">{item.month}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="text-sm font-medium">{item.count} reviews</div>
                        <div className="text-xs text-muted-foreground">
                          Avg: {item.rating.toFixed(1)}/10
                        </div>
                      </div>
                      <Progress value={(item.rating / 10) * 100} className="w-16" />
                    </div>
                  </div>
                ))}
                {monthlyChartData.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No trend data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Reviews by Channel</CardTitle>
                <CardDescription>Distribution of reviews across booking channels</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {channelChartData.map((channel, index) => {
                    const percentage = (channel.count / reviews.length) * 100
                    return (
                      <div key={channel.name} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: COLORS[index % COLORS.length] }}
                            />
                            <span className="font-medium">{channel.name}</span>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {channel.count} ({percentage.toFixed(1)}%)
                          </span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    )
                  })}
                  {channelChartData.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No channel data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Channel Performance</CardTitle>
                <CardDescription>Average rating by booking channel</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {channelChartData.map((channel, index) => (
                    <div key={channel.name} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        />
                        <span className="font-medium">{channel.name}</span>
                        <Badge variant="outline">{channel.count} reviews</Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Star className="h-4 w-4 text-yellow-500" fill="currentColor" />
                        <span className="font-bold">{channel.rating.toFixed(1)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Category Performance</CardTitle>
              <CardDescription>Average ratings across different review categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryChartData.map((category) => (
                  <div key={category.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{category.name}</span>
                      <div className="flex items-center space-x-2">
                        <Star className="h-4 w-4 text-yellow-500" fill="currentColor" />
                        <span className="font-bold">{category.rating.toFixed(1)}/10</span>
                        <Badge variant="outline" className="text-xs">
                          {category.count} reviews
                        </Badge>
                      </div>
                    </div>
                    <Progress value={(category.rating / 10) * 100} className="h-2" />
                  </div>
                ))}
                {categoryChartData.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No category data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ratings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rating Distribution</CardTitle>
              <CardDescription>How your reviews are distributed across rating ranges</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {ratingChartData.map((item) => (
                  <div key={item.range} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium w-16">{item.range} stars</span>
                      <Progress value={item.percentage} className="w-32" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">
                        {item.count} reviews ({item.percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Analytics
