"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Link } from "react-router-dom"
import {
  reviewsApi,
  propertiesApi,
  hostawayApi,
  type Review,
  type Property,
  type ReviewStatistics,
} from "../../services/api"
import ReviewsList from "./ReviewsList"
import PropertyStats from "./PropertyStats"
import PropertyList from "./PropertyList"
import ReviewFilters from "./ReviewFilters"
import Analytics from "./Analytics"
import "./Dashboard.css"
import { RefreshCw, Download, Upload, BarChart3, Building2, TrendingUp, Globe, ArrowRight } from "lucide-react"

const Dashboard: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([])
  const [properties, setProperties] = useState<Property[]>([])
  const [statistics, setStatistics] = useState<ReviewStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedProperty, setSelectedProperty] = useState<string>("")
  const [filters, setFilters] = useState({
    channel: "",
    rating: "",
    status: "",
    showOnWebsite: "",
    startDate: "",
    endDate: "",
  })
  const [activeTab, setActiveTab] = useState<"reviews" | "properties" | "analytics">("reviews")

  const handleSync = () => {
    // Placeholder for sync logic
  }

  const handleExport = () => {
    // Placeholder for export logic
  }

  const handleReviewUpdate = (updatedReview: Review) => {
    // Placeholder for review update logic
  }

  useEffect(() => {
    // Fetch reviews, properties, and statistics
    const fetchData = async () => {
      try {
        const reviewsData = await reviewsApi.getReviews()
        const propertiesData = await propertiesApi.getProperties()
        const statisticsData = await hostawayApi.getReviewStatistics()
        setReviews(reviewsData)
        setProperties(propertiesData)
        setStatistics(statisticsData)
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading && reviews.length === 0) {
    return (
      <div className="dashboard-loading">
        <div className="spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    )
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <div className="header-title-section">
            <h1>Hostaway Reviews Dashboard</h1>
            <p className="subtitle">
              Manage and analyze guest reviews from Hostaway properties with powerful insights and automation tools
            </p>
          </div>

          <div className="integration-card">
            <div className="integration-content">
              <Globe size={24} />
              <div className="integration-text">
                <h3>Google Places Reviews</h3>
                <p>Access Google reviews for any property or business location</p>
              </div>
            </div>
            <Link to="/google-places" className="integration-link">
              <span>Explore</span>
              <ArrowRight size={16} />
            </Link>
          </div>
        </div>
        <div className="header-actions">
          <button className="btn btn-secondary" onClick={handleSync}>
            <RefreshCw size={18} />
            Sync Data
          </button>
          <button className="btn btn-secondary" onClick={handleExport}>
            <Download size={18} />
            Export CSV
          </button>
          <button className="btn btn-primary">
            <Upload size={18} />
            Import Reviews
          </button>
        </div>
      </div>

      <PropertyStats properties={properties} statistics={statistics} />

      <div className="dashboard-content">
        <div className="content-header">
          <div className="tabs">
            <button
              className={`tab ${activeTab === "reviews" ? "active" : ""}`}
              onClick={() => setActiveTab("reviews")}
            >
              <BarChart3 size={16} />
              Reviews Management
            </button>
            <button
              className={`tab ${activeTab === "properties" ? "active" : ""}`}
              onClick={() => setActiveTab("properties")}
            >
              <Building2 size={16} />
              Properties
            </button>
            <button
              className={`tab ${activeTab === "analytics" ? "active" : ""}`}
              onClick={() => setActiveTab("analytics")}
            >
              <TrendingUp size={16} />
              Analytics & Insights
            </button>
          </div>
        </div>

        {activeTab === "reviews" && (
          <div className="reviews-section">
            <ReviewFilters
              filters={filters}
              onFilterChange={setFilters}
              properties={properties}
              selectedProperty={selectedProperty}
              onPropertyChange={setSelectedProperty}
            />
            <ReviewsList reviews={reviews} onUpdateReview={handleReviewUpdate} loading={loading} />
          </div>
        )}

        {activeTab === "properties" && (
          <div className="properties-section">
            <PropertyList properties={properties} loading={loading} />
          </div>
        )}

        {activeTab === "analytics" && (
          <div className="analytics-section">
            <Analytics statistics={statistics} reviews={reviews} selectedProperty={selectedProperty} />
          </div>
        )}
      </div>
    </div>
  )
}

export default Dashboard
