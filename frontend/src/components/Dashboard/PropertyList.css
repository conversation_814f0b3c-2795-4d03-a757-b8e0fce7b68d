.property-list {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  margin-bottom: 2rem;
}

.property-list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.property-list-loading p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.no-properties {
  text-align: center;
  padding: 3rem;
}

.no-properties h3 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.no-properties p {
  color: var(--color-text-secondary);
}

.property-list-header {
  margin-bottom: 2rem;
}

.property-list-header h3 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.property-list-header .subtitle {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.property-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: transform var(--transition-fast);
}

.property-card-link:hover {
  transform: translateY(-4px);
}

.property-card {
  border: 1px solid var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: var(--color-white);
  transition: all var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.property-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--color-accent);
}

.property-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.property-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.property-card:hover .property-image img {
  transform: scale(1.05);
}

.property-type-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: var(--color-primary);
  color: var(--color-white);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.property-rating-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--color-accent);
  color: var(--color-primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.property-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.property-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.property-name {
  color: var(--color-primary);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
}

.property-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.property-features {
  display: flex;
  gap: 1rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-primary);
  font-size: 0.875rem;
}

.property-description {
  color: var(--color-text-primary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.property-stats {
  display: flex;
  gap: 1rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.property-amenities-preview {
  margin-top: auto;
}

.amenities-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.amenity-tag {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.amenity-tag.more {
  background: var(--color-accent);
  color: var(--color-primary);
}

.property-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--color-bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-bg-primary);
}

.property-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-indicator.active {
  background: var(--color-success);
  color: white;
}

.status-indicator.inactive {
  background: var(--color-error);
  color: white;
}

.view-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.property-card:hover .view-details {
  color: var(--color-accent);
}

/* Responsive Design */
@media (max-width: 768px) {
  .properties-grid {
    grid-template-columns: 1fr;
  }
  
  .property-features {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .property-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .property-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .property-list {
    padding: 1rem;
  }
  
  .property-content {
    padding: 1rem;
  }
  
  .property-footer {
    padding: 1rem;
  }
}
