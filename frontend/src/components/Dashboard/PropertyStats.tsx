"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Building2, Star, TrendingUp, Users, MapPin, Calendar } from "lucide-react"
import { type Property, type ReviewStatistics } from "../../services/api"

interface PropertyStatsProps {
  properties: Property[]
  statistics: ReviewStatistics | null
}

const PropertyStats: React.FC<PropertyStatsProps> = ({ properties, statistics }) => {
  const stats = statistics?.overview || {
    avgRating: 0,
    totalReviews: 0,
    publishedReviews: 0,
    websiteReviews: 0
  }

  const activeProperties = properties.filter(p => p.isActive)
  const totalProperties = properties.length
  const inactiveProperties = totalProperties - activeProperties.length

  // Calculate property type distribution
  const propertyTypes = properties.reduce((acc, property) => {
    acc[property.type] = (acc[property.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Calculate average ratings distribution
  const ratingDistribution = properties.reduce((acc, property) => {
    if (property.avgRating) {
      const ratingRange = Math.floor(property.avgRating / 2) * 2 // Group by 2s: 0-2, 2-4, 4-6, 6-8, 8-10
      const key = `${ratingRange}-${ratingRange + 2}`
      acc[key] = (acc[key] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)

  // Calculate occupancy metrics
  const totalCapacity = properties.reduce((sum, property) => sum + property.maxGuests, 0)
  const avgCapacity = totalProperties > 0 ? Math.round(totalCapacity / totalProperties) : 0

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return "text-green-600"
    if (rating >= 6) return "text-yellow-600"
    return "text-red-600"
  }

  const getRatingBadgeVariant = (rating: number): "default" | "secondary" | "destructive" => {
    if (rating >= 8) return "default"
    if (rating >= 6) return "secondary"
    return "destructive"
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
      {/* Total Properties */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalProperties}</div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <span className="text-green-600">{activeProperties.length} active</span>
            <Separator orientation="vertical" className="h-3" />
            <span className="text-red-600">{inactiveProperties} inactive</span>
          </div>
          <Progress 
            value={totalProperties > 0 ? (activeProperties.length / totalProperties) * 100 : 0} 
            className="mt-2 h-1"
          />
        </CardContent>
      </Card>

      {/* Average Rating */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
          <Star className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${getRatingColor(stats.avgRating)}`}>
            {stats.avgRating ? stats.avgRating.toFixed(1) : '0.0'}
          </div>
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <span>{'⭐'.repeat(Math.round(stats.avgRating / 2))}</span>
            <span>({stats.totalReviews} reviews)</span>
          </div>
          <div className="mt-2 text-xs">
            <Badge variant={getRatingBadgeVariant(stats.avgRating)} className="text-xs">
              {stats.avgRating >= 8 ? 'Excellent' : stats.avgRating >= 6 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Total Reviews */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalReviews}</div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <span className="text-green-600">{stats.publishedReviews} published</span>
            <Separator orientation="vertical" className="h-3" />
            <span className="text-blue-600">{stats.websiteReviews} on website</span>
          </div>
          <Progress 
            value={stats.totalReviews > 0 ? (stats.publishedReviews / stats.totalReviews) * 100 : 0} 
            className="mt-2 h-1"
          />
        </CardContent>
      </Card>

      {/* Guest Capacity */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Guest Capacity</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalCapacity}</div>
          <div className="text-xs text-muted-foreground">
            Avg {avgCapacity} guests per property
          </div>
          <div className="mt-2 flex flex-wrap gap-1">
            {Object.entries(propertyTypes).slice(0, 2).map(([type, count]) => (
              <Badge key={type} variant="outline" className="text-xs">
                {count} {type}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Property Types Distribution */}
      {Object.keys(propertyTypes).length > 0 && (
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Property Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(propertyTypes).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="text-sm">{type}</span>
                  <div className="flex items-center space-x-2">
                    <Progress 
                      value={(count / totalProperties) * 100} 
                      className="w-20 h-2"
                    />
                    <span className="text-sm font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Rating Distribution */}
      {Object.keys(ratingDistribution).length > 0 && (
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Star className="h-4 w-4" />
              Rating Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(ratingDistribution).map(([range, count]) => (
                <div key={range} className="flex items-center justify-between">
                  <span className="text-sm">{range} stars</span>
                  <div className="flex items-center space-x-2">
                    <Progress 
                      value={(count / totalProperties) * 100} 
                      className="w-20 h-2"
                    />
                    <span className="text-sm font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default PropertyStats
