"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  CalendarIcon, 
  Filter, 
  X, 
  Search,
  Star,
  Building2,
  Globe
} from "lucide-react"
import { cn } from "@/lib/utils"

// Simple date formatting functions
const formatDateISO = (date: Date): string => {
  return date.toISOString().split('T')[0]
}

const formatDatePretty = (date: Date): string => {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric"
  })
}
import { type Property } from "../../services/api"

interface ReviewFiltersProps {
  filters: {
    channel: string
    rating: string
    status: string
    showOnWebsite: string
    startDate: string
    endDate: string
  }
  onFilterChange: (filters: any) => void
  properties: Property[]
  selectedProperty: string
  onPropertyChange: (propertyId: string) => void
}

const ReviewFilters: React.FC<ReviewFiltersProps> = ({
  filters,
  onFilterChange,
  properties,
  selectedProperty,
  onPropertyChange,
}) => {
  const [startDate, setStartDate] = React.useState<Date>()
  const [endDate, setEndDate] = React.useState<Date>()
  const [searchQuery, setSearchQuery] = React.useState("")

  const handleFilterChange = (key: string, value: string) => {
    onFilterChange({ ...filters, [key]: value })
  }

  const handleDateChange = (type: 'start' | 'end', date: Date | undefined) => {
    if (type === 'start') {
      setStartDate(date)
      handleFilterChange('startDate', date ? formatDateISO(date) : '')
    } else {
      setEndDate(date)
      handleFilterChange('endDate', date ? formatDateISO(date) : '')
    }
  }

  const clearAllFilters = () => {
    onFilterChange({
      channel: "",
      rating: "",
      status: "",
      showOnWebsite: "",
      startDate: "",
      endDate: "",
    })
    onPropertyChange("")
    setStartDate(undefined)
    setEndDate(undefined)
    setSearchQuery("")
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (filters.channel) count++
    if (filters.rating) count++
    if (filters.status) count++
    if (filters.showOnWebsite) count++
    if (filters.startDate) count++
    if (filters.endDate) count++
    if (selectedProperty) count++
    if (searchQuery) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <CardTitle className="text-lg">Filters</CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </div>
          {activeFiltersCount > 0 && (
            <Button variant="outline" size="sm" onClick={clearAllFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Search */}
          <div className="space-y-2">
            <Label htmlFor="search">Search Reviews</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search guest name, review text..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Property Filter */}
          <div className="space-y-2">
            <Label>Property</Label>
            <Select value={selectedProperty} onValueChange={onPropertyChange}>
              <SelectTrigger>
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2 text-muted-foreground" />
                  <SelectValue placeholder="All properties" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All properties</SelectItem>
                {properties.map((property) => (
                  <SelectItem key={property._id} value={property.externalId}>
                    <div className="flex items-center justify-between w-full">
                      <span className="truncate">{property.name}</span>
                      {property.avgRating && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          {property.avgRating.toFixed(1)}★
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Channel Filter */}
          <div className="space-y-2">
            <Label>Channel</Label>
            <Select value={filters.channel} onValueChange={(value) => handleFilterChange('channel', value)}>
              <SelectTrigger>
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2 text-muted-foreground" />
                  <SelectValue placeholder="All channels" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All channels</SelectItem>
                <SelectItem value="Airbnb">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    Airbnb
                  </div>
                </SelectItem>
                <SelectItem value="Booking.com">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    Booking.com
                  </div>
                </SelectItem>
                <SelectItem value="Direct">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Direct
                  </div>
                </SelectItem>
                <SelectItem value="VRBO">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    VRBO
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Rating Filter */}
          <div className="space-y-2">
            <Label>Rating</Label>
            <Select value={filters.rating} onValueChange={(value) => handleFilterChange('rating', value)}>
              <SelectTrigger>
                <div className="flex items-center">
                  <Star className="h-4 w-4 mr-2 text-muted-foreground" />
                  <SelectValue placeholder="All ratings" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All ratings</SelectItem>
                <SelectItem value="9-10">
                  <div className="flex items-center">
                    <div className="flex mr-2">
                      {'⭐'.repeat(5)}
                    </div>
                    9-10 (Excellent)
                  </div>
                </SelectItem>
                <SelectItem value="7-8">
                  <div className="flex items-center">
                    <div className="flex mr-2">
                      {'⭐'.repeat(4)}
                    </div>
                    7-8 (Good)
                  </div>
                </SelectItem>
                <SelectItem value="5-6">
                  <div className="flex items-center">
                    <div className="flex mr-2">
                      {'⭐'.repeat(3)}
                    </div>
                    5-6 (Average)
                  </div>
                </SelectItem>
                <SelectItem value="1-4">
                  <div className="flex items-center">
                    <div className="flex mr-2">
                      {'⭐'.repeat(2)}
                    </div>
                    1-4 (Poor)
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label>Status</Label>
            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All statuses</SelectItem>
                <SelectItem value="published">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    Published
                  </div>
                </SelectItem>
                <SelectItem value="pending">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    Pending
                  </div>
                </SelectItem>
                <SelectItem value="rejected">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    Rejected
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Website Visibility Filter */}
          <div className="space-y-2">
            <Label>Website Visibility</Label>
            <Select value={filters.showOnWebsite} onValueChange={(value) => handleFilterChange('showOnWebsite', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All reviews" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All reviews</SelectItem>
                <SelectItem value="true">Shown on website</SelectItem>
                <SelectItem value="false">Hidden from website</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !startDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? formatDatePretty(startDate) : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => handleDateChange('start', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <Label>End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !endDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? formatDatePretty(endDate) : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={(date) => handleDateChange('end', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Active Filters Display */}
        {activeFiltersCount > 0 && (
          <>
            <Separator className="my-4" />
            <div className="flex flex-wrap gap-2">
              <span className="text-sm font-medium text-muted-foreground">Active filters:</span>
              {selectedProperty && (
                <Badge variant="secondary" className="text-xs">
                  Property: {properties.find(p => p.externalId === selectedProperty)?.name}
                  <X 
                    className="ml-1 h-3 w-3 cursor-pointer" 
                    onClick={() => onPropertyChange("")}
                  />
                </Badge>
              )}
              {filters.channel && (
                <Badge variant="secondary" className="text-xs">
                  Channel: {filters.channel}
                  <X 
                    className="ml-1 h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange('channel', '')}
                  />
                </Badge>
              )}
              {filters.rating && (
                <Badge variant="secondary" className="text-xs">
                  Rating: {filters.rating}
                  <X 
                    className="ml-1 h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange('rating', '')}
                  />
                </Badge>
              )}
              {filters.status && (
                <Badge variant="secondary" className="text-xs">
                  Status: {filters.status}
                  <X 
                    className="ml-1 h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange('status', '')}
                  />
                </Badge>
              )}
              {filters.showOnWebsite && (
                <Badge variant="secondary" className="text-xs">
                  Website: {filters.showOnWebsite === 'true' ? 'Shown' : 'Hidden'}
                  <X 
                    className="ml-1 h-3 w-3 cursor-pointer" 
                    onClick={() => handleFilterChange('showOnWebsite', '')}
                  />
                </Badge>
              )}
              {searchQuery && (
                <Badge variant="secondary" className="text-xs">
                  Search: "{searchQuery}"
                  <X 
                    className="ml-1 h-3 w-3 cursor-pointer" 
                    onClick={() => setSearchQuery('')}
                  />
                </Badge>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default ReviewFilters
