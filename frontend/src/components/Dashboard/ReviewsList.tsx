"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Star, 
  MoreHorizontal, 
  Eye, 
  EyeOff, 
  ThumbsUp, 
  MessageSquare,
  Calendar,
  User,
  Building2
} from "lucide-react"
import { type Review } from "../../services/api"

interface ReviewsListProps {
  reviews: Review[]
  onUpdateReview: (review: Review) => void
  loading: boolean
}

const ReviewsList: React.FC<ReviewsListProps> = ({ reviews, onUpdateReview, loading }) => {
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('cards')

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return "text-green-600"
    if (rating >= 6) return "text-yellow-600"
    return "text-red-600"
  }

  const getRatingBadgeVariant = (rating: number): "default" | "secondary" | "destructive" => {
    if (rating >= 8) return "default"
    if (rating >= 6) return "secondary"
    return "destructive"
  }

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" => {
    switch (status.toLowerCase()) {
      case 'published': return "default"
      case 'pending': return "secondary"
      case 'rejected': return "destructive"
      default: return "secondary"
    }
  }

  const getChannelColor = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'airbnb': return "bg-red-100 text-red-800"
      case 'booking.com': return "bg-blue-100 text-blue-800"
      case 'direct': return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const handleToggleWebsite = (review: Review) => {
    onUpdateReview({ ...review, showOnWebsite: !review.showOnWebsite })
  }

  const handleMarkHelpful = (review: Review) => {
    onUpdateReview({ ...review, helpfulCount: (review.helpfulCount || 0) + 1 })
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Reviews</CardTitle>
          <CardDescription>Loading reviews...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[250px]" />
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (reviews.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Reviews</CardTitle>
          <CardDescription>No reviews found</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by syncing data from Hostaway or adding reviews manually.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (viewMode === 'table') {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Reviews ({reviews.length})</CardTitle>
              <CardDescription>Manage and analyze guest reviews</CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setViewMode('cards')}
            >
              Card View
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Guest</TableHead>
                <TableHead>Property</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Channel</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reviews.map((review) => (
                <TableRow key={review._id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {review.guestName?.charAt(0) || 'G'}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{review.guestName || 'Anonymous'}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-[200px] truncate">
                      {review.listingName}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Star className={`h-4 w-4 ${getRatingColor(review.rating)}`} fill="currentColor" />
                      <span className={`font-medium ${getRatingColor(review.rating)}`}>
                        {review.rating}/10
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getChannelColor(review.channel)}>
                      {review.channel}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(review.status)}>
                      {review.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(review.submittedAt)}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleToggleWebsite(review)}>
                          {review.showOnWebsite ? (
                            <>
                              <EyeOff className="mr-2 h-4 w-4" />
                              Hide from website
                            </>
                          ) : (
                            <>
                              <Eye className="mr-2 h-4 w-4" />
                              Show on website
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleMarkHelpful(review)}>
                          <ThumbsUp className="mr-2 h-4 w-4" />
                          Mark helpful ({review.helpfulCount || 0})
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>View details</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Reviews ({reviews.length})</CardTitle>
            <CardDescription>Manage and analyze guest reviews</CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setViewMode('table')}
          >
            Table View
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review._id} className="p-4">
              <div className="flex items-start justify-between space-x-4">
                <div className="flex items-start space-x-3 flex-1">
                  <Avatar>
                    <AvatarFallback>
                      {review.guestName?.charAt(0) || 'G'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium">{review.guestName || 'Anonymous Guest'}</h4>
                      <Badge variant={getStatusBadgeVariant(review.status)} className="text-xs">
                        {review.status}
                      </Badge>
                      {review.showOnWebsite && (
                        <Badge variant="outline" className="text-xs">
                          <Eye className="w-3 h-3 mr-1" />
                          Website
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                      <div className="flex items-center space-x-1">
                        <Building2 className="h-3 w-3" />
                        <span className="truncate">{review.listingName}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(review.submittedAt)}</span>
                      </div>
                      <Badge variant="outline" className={getChannelColor(review.channel)}>
                        {review.channel}
                      </Badge>
                    </div>
                    <p className="text-sm leading-relaxed mb-3">
                      {review.publicReview || 'No review text available'}
                    </p>
                    {review.helpfulCount && review.helpfulCount > 0 && (
                      <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                        <ThumbsUp className="h-3 w-3" />
                        <span>{review.helpfulCount} found this helpful</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <div className="flex items-center space-x-1">
                    <Star className={`h-4 w-4 ${getRatingColor(review.rating)}`} fill="currentColor" />
                    <span className={`font-bold ${getRatingColor(review.rating)}`}>
                      {review.rating}/10
                    </span>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleToggleWebsite(review)}>
                        {review.showOnWebsite ? (
                          <>
                            <EyeOff className="mr-2 h-4 w-4" />
                            Hide from website
                          </>
                        ) : (
                          <>
                            <Eye className="mr-2 h-4 w-4" />
                            Show on website
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleMarkHelpful(review)}>
                        <ThumbsUp className="mr-2 h-4 w-4" />
                        Mark helpful
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>View details</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default ReviewsList
