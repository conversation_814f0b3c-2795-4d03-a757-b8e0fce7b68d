"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Search, MapPin, Star, Globe, AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import "./GooglePlaces.css"

interface GooglePlace {
  placeId: string
  name: string
  address: string
  rating: number
  totalRatings: number
  reviews: GoogleReview[]
}

interface GoogleReview {
  _id: string
  rating: number
  publicReview: string
  submittedAt: string
  guestName: string
  channel: string
}

interface GooglePlaceDetails {
  name: string
  address: string
  rating: number
  totalRatings: number
  reviews: GoogleReview[]
}

const GooglePlaces: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<GooglePlace[]>([])
  const [selectedPlace, setSelectedPlace] = useState<GooglePlaceDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [searchLoading, setSearchLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [apiStatus, setApiStatus] = useState<any>(null)

  useEffect(() => {
    checkApiStatus()
  }, [])

  const checkApiStatus = async () => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'
      const response = await fetch(`${API_BASE_URL}/api/google/status`)
      const data = await response.json()
      setApiStatus(data.data || data)
    } catch (error) {
      console.error("Failed to check API status:", error)
      setApiStatus({ configured: false, message: "Using mock data" })
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setSearchLoading(true)
    setError(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'
      const response = await fetch(
        `${API_BASE_URL}/api/google/place-search?query=${encodeURIComponent(searchQuery)}`,
      )
      const data = await response.json()

      if (data.success) {
        setSearchResults(data.data || [])
      } else {
        setError(data.message || "Failed to search places")
        setSearchResults([
          {
            placeId: "mock_place_1",
            name: `${searchQuery} - Sample Location`,
            address: "123 Sample Street, Sample City",
            rating: 4.5,
            totalRatings: 127,
            reviews: [],
          },
        ])
      }
    } catch (error) {
      setError("Failed to search places. Using mock data for demonstration.")
      setSearchResults([
        {
          placeId: "mock_place_1",
          name: `${searchQuery} - Sample Location`,
          address: "123 Sample Street, Sample City",
          rating: 4.5,
          totalRatings: 127,
          reviews: [],
        },
      ])
    } finally {
      setSearchLoading(false)
    }
  }

  const handlePlaceSelect = async (placeId: string) => {
    setLoading(true)
    setError(null)
    setSelectedPlace(null)

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.REACT_APP_API_URL || 'http://localhost:8000'
      const response = await fetch(
        `${API_BASE_URL}/api/google/reviews?placeId=${encodeURIComponent(placeId)}`,
      )
      const data = await response.json()

      if (data.status === "success" && data.data) {
        setSelectedPlace(data.data)
      } else {
        setError(data.message || "Failed to load place reviews")
        setSelectedPlace({
          name: "Sample FlexLiving Property",
          address: "123 Sample Street, Sample City",
          rating: 4.5,
          totalRatings: 127,
          reviews: [
            {
              _id: "mock_review_1",
              rating: 9,
              publicReview:
                "Excellent stay! The property was clean, modern, and perfectly located. The host was very responsive and helpful throughout our stay.",
              submittedAt: "2024-01-15T10:30:00Z",
              guestName: "Sarah Johnson",
              channel: "Google Reviews",
            },
            {
              _id: "mock_review_2",
              rating: 8,
              publicReview:
                "Great location and beautiful apartment. Everything was as described and the check-in process was smooth.",
              submittedAt: "2024-01-10T14:20:00Z",
              guestName: "Michael Chen",
              channel: "Google Reviews",
            },
            {
              _id: "mock_review_3",
              rating: 7,
              publicReview:
                "Good value for money. The apartment was comfortable and well-equipped. Minor issues with WiFi but overall a pleasant stay.",
              submittedAt: "2024-01-05T09:15:00Z",
              guestName: "Emma Wilson",
              channel: "Google Reviews",
            },
          ],
        })
      }
    } catch (error) {
      setError("Failed to load place reviews. Showing mock data for demonstration.")
      setSelectedPlace({
        name: "Sample FlexLiving Property",
        address: "123 Sample Street, Sample City",
        rating: 4.5,
        totalRatings: 127,
        reviews: [
          {
            _id: "mock_review_1",
            rating: 9,
            publicReview:
              "Excellent stay! The property was clean, modern, and perfectly located. The host was very responsive and helpful throughout our stay.",
            submittedAt: "2024-01-15T10:30:00Z",
            guestName: "Sarah Johnson",
            channel: "Google Reviews",
          },
          {
            _id: "mock_review_2",
            rating: 8,
            publicReview:
              "Great location and beautiful apartment. Everything was as described and the check-in process was smooth.",
            submittedAt: "2024-01-10T14:20:00Z",
            guestName: "Michael Chen",
            channel: "Google Reviews",
          },
          {
            _id: "mock_review_3",
            rating: 7,
            publicReview:
              "Good value for money. The apartment was comfortable and well-equipped. Minor issues with WiFi but overall a pleasant stay.",
            submittedAt: "2024-01-05T09:15:00Z",
            guestName: "Emma Wilson",
            channel: "Google Reviews",
          },
        ],
      })
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 9) return "rating-excellent"
    if (rating >= 7) return "rating-good"
    if (rating >= 5) return "rating-average"
    return "rating-poor"
  }

  return (
    <div className="google-places">
      <div className="google-places-header">
        <div className="header-content">
          <div className="header-title">
            <Globe size={32} />
            <div>
              <h1>Google Places Reviews</h1>
              <p className="subtitle">Search and view Google reviews for any property or business location</p>
            </div>
          </div>

          {apiStatus && (
            <div className={`api-status ${apiStatus.configured ? "configured" : "not-configured"}`}>
              {apiStatus.configured ? (
                <div className="status-item">
                  <CheckCircle size={16} />
                  <span>API Configured</span>
                </div>
              ) : (
                <div className="status-item">
                  <AlertCircle size={16} />
                  <span>{apiStatus.message}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="google-places-content">
        <div className="search-section">
          <div className="search-container">
            <div className="search-input-group">
              <Search size={20} />
              <input
                type="text"
                placeholder="Search for a property or business (e.g., 'FlexLiving London', 'Hotel Name City')"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                className="search-input"
              />
              <button onClick={handleSearch} disabled={searchLoading || !searchQuery.trim()} className="search-button">
                {searchLoading ? <Loader2 size={16} className="animate-spin" /> : "Search"}
              </button>
            </div>
          </div>

          {error && (
            <div className="error-message">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          )}
        </div>

        {searchResults.length > 0 && (
          <div className="search-results">
            <h3>Search Results</h3>
            <div className="places-grid">
              {searchResults.map((place) => (
                <div key={place.placeId} className="place-card" onClick={() => handlePlaceSelect(place.placeId)}>
                  <div className="place-header">
                    <h4>{place.name}</h4>
                    <div className="place-rating">
                      <Star size={16} fill="currentColor" />
                      <span>{place.rating}</span>
                      <span className="rating-count">({place.totalRatings})</span>
                    </div>
                  </div>
                  <div className="place-address">
                    <MapPin size={14} />
                    <span>{place.address}</span>
                  </div>
                  <div className="place-reviews-count">{place.reviews.length} reviews available</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {loading && (
          <div className="loading-container">
            <Loader2 size={32} className="animate-spin" />
            <p>Loading place reviews...</p>
          </div>
        )}

        {selectedPlace && (
          <div className="place-details">
            <div className="place-details-header">
              <div className="place-info">
                <h2>{selectedPlace.name}</h2>
                <div className="place-meta">
                  <div className="place-address">
                    <MapPin size={16} />
                    <span>{selectedPlace.address}</span>
                  </div>
                  <div className="place-rating-summary">
                    <Star size={16} fill="currentColor" />
                    <span>{selectedPlace.rating} rating</span>
                    <span className="rating-count">({selectedPlace.totalRatings} total reviews)</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="reviews-section">
              <h3>Google Reviews ({selectedPlace.reviews.length})</h3>

              {selectedPlace.reviews.length === 0 ? (
                <div className="no-reviews">
                  <p>No reviews available for this location.</p>
                </div>
              ) : (
                <div className="reviews-list">
                  {selectedPlace.reviews.map((review) => (
                    <div key={review._id} className="review-card">
                      <div className="review-header">
                        <div className="reviewer-info">
                          <span className="reviewer-name">{review.guestName}</span>
                          <span className="review-date">{formatDate(review.submittedAt)}</span>
                        </div>
                        <div className={`review-rating ${getRatingColor(review.rating)}`}>
                          <Star size={16} fill="currentColor" />
                          <span>{review.rating}/10</span>
                        </div>
                      </div>
                      <div className="review-content">
                        <p>{review.publicReview}</p>
                      </div>
                      <div className="review-footer">
                        <span className="review-source">Source: {review.channel}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {!searchResults.length && !selectedPlace && !loading && !searchLoading && (
          <div className="empty-state">
            <Globe size={64} />
            <h3>Search for Google Places Reviews</h3>
            <p>Enter a property name, business name, or address to find and view Google reviews.</p>
            <div className="example-searches">
              <p>Try searching for:</p>
              <ul>
                <li>"FlexLiving London"</li>
                <li>"Hotel Name + City"</li>
                <li>"Restaurant Name + Location"</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default GooglePlaces
