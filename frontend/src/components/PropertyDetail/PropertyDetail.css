.property-detail {
  min-height: 100vh;
  background-color: var(--color-bg-primary);
}

.property-detail-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
}

.property-detail-loading p {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.property-detail-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
  text-align: center;
}

.property-detail-error h2 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.property-detail-error p {
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
}

/* Header */
.property-detail-header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-bg-secondary);
  padding: 2rem 0;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  transition: color var(--transition-fast);
}

.back-link:hover {
  color: var(--color-primary);
}

.property-header-content h1 {
  color: var(--color-primary);
  margin-bottom: 1rem;
  font-size: 2.5rem;
}

.property-meta {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.property-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-secondary);
  font-size: 1rem;
}

.property-features {
  display: flex;
  gap: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-text-primary);
  font-size: 0.875rem;
}

/* Property Image */
.property-image-section {
  background-color: var(--color-white);
  padding: 2rem 0;
}

.property-main-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

/* Tabs */
.property-tabs {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-bg-secondary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.tabs {
  display: flex;
  gap: 0.5rem;
}

.tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tab:hover {
  color: var(--color-text-primary);
}

.tab.active {
  color: var(--color-primary);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-accent);
}

/* Content */
.property-content {
  padding: 2rem 0;
}

/* Overview Content */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.stat-card {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: var(--color-accent);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.property-description {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
}

.property-description h3 {
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.property-description p {
  color: var(--color-text-primary);
  line-height: 1.6;
}

.property-amenities {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
}

.property-amenities h3 {
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.amenity-item {
  background: var(--color-bg-secondary);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  color: var(--color-text-primary);
  font-size: 0.875rem;
}

/* Reviews Content */
.reviews-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.review-filters {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.filters-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.filters-header h3 {
  color: var(--color-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-primary);
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  background: var(--color-white);
  color: var(--color-text-primary);
  font-size: 0.875rem;
}

.reviews-list {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.reviews-header h3 {
  color: var(--color-primary);
  margin-bottom: 1.5rem;
}

.reviews-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-card {
  border: 1px solid var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: all var(--transition-fast);
}

.review-card:hover {
  box-shadow: var(--shadow-sm);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.reviewer-info {
  display: flex;
  gap: 1rem;
}

.reviewer-avatar {
  width: 40px;
  height: 40px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.reviewer-details h4 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.review-meta {
  display: flex;
  gap: 1rem;
}

.review-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.channel-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.review-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-accent);
  font-weight: 600;
}

.review-content p {
  color: var(--color-text-primary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.review-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.review-status {
  display: flex;
  gap: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.published {
  background: var(--color-success);
  color: white;
}

.status-badge.pending {
  background: var(--color-warning);
  color: white;
}

.status-badge.rejected {
  background: var(--color-error);
  color: white;
}

.website-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  background: var(--color-info);
  color: white;
}

.review-actions {
  display: flex;
  gap: 0.5rem;
}

.helpful-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  background: none;
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
}

.helpful-btn:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-text-secondary);
}

.review-response {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-accent);
}

.review-response h5 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.review-response p {
  color: var(--color-text-primary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.no-reviews {
  text-align: center;
  padding: 3rem;
  color: var(--color-text-secondary);
}

/* Analytics Content */
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.analytics-overview {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
}

.analytics-overview h3 {
  color: var(--color-primary);
  margin-bottom: 1.5rem;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.analytics-card {
  border: 1px solid var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

.analytics-card h4 {
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.channel-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.channel-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.channel-item .channel-name {
  font-weight: 500;
  color: var(--color-text-primary);
}

.channel-item .channel-count {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.channel-bar {
  height: 8px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.channel-fill {
  height: 100%;
  background: var(--color-accent);
  transition: width var(--transition-normal);
}

.category-breakdown {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
}

.category-name {
  font-weight: 500;
  color: var(--color-text-primary);
  text-transform: capitalize;
}

.category-rating {
  font-weight: 600;
  color: var(--color-primary);
}

.monthly-trends {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
}

.monthly-trends h3 {
  color: var(--color-primary);
  margin-bottom: 1.5rem;
}

.trends-chart {
  display: flex;
  gap: 1rem;
  align-items: end;
  height: 200px;
  padding: 1rem 0;
}

.trend-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  height: 100%;
}

.trend-month {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.trend-bar {
  flex: 1;
  width: 20px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: end;
  min-height: 20px;
}

.trend-fill {
  width: 100%;
  background: var(--color-accent);
  border-radius: var(--radius-sm);
  min-height: 4px;
  transition: height var(--transition-normal);
}

.trend-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.trend-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-primary);
}

.trend-rating {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .quick-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .property-features {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .property-header-content h1 {
    font-size: 2rem;
  }
  
  .property-main-image {
    height: 250px;
  }
  
  .amenities-grid {
    grid-template-columns: 1fr;
  }
}
