"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Star, 
  MapPin, 
  Users, 
  Bed, 
  Bath,
  Calendar,
  Eye,
  Edit,
  BarChart3,
  ExternalLink,
  Building2,
  TrendingUp,
  MessageSquare
} from "lucide-react"
import { reviewsApi, propertiesApi, type Review, type Property } from "../../services/api"

const PropertyDetail: React.FC = () => {
  const { propertyId } = useParams<{ propertyId: string }>()
  const [property, setProperty] = useState<Property | null>(null)
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadData = async () => {
      if (!propertyId) return

      try {
        setLoading(true)
        setError(null)

        const [propertyData, reviewsData] = await Promise.all([
          propertiesApi.getPropertyById(propertyId),
          reviewsApi.getReviews({ listingId: propertyId })
        ])

        setProperty(propertyData)
        setReviews(reviewsData)
      } catch (err) {
        console.error('Error loading property details:', err)
        setError(err instanceof Error ? err.message : 'Failed to load property details')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [propertyId])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return "text-green-600"
    if (rating >= 6) return "text-yellow-600"
    return "text-red-600"
  }

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" => {
    switch (status.toLowerCase()) {
      case 'published': return "default"
      case 'pending': return "secondary"
      case 'rejected': return "destructive"
      default: return "secondary"
    }
  }

  // Calculate statistics
  const publishedReviews = reviews.filter(r => r.status === 'published')
  const websiteReviews = reviews.filter(r => r.showOnWebsite)
  const averageRating = publishedReviews.length > 0 
    ? publishedReviews.reduce((sum, review) => sum + review.rating, 0) / publishedReviews.length 
    : 0

  const ratingDistribution = publishedReviews.reduce((acc, review) => {
    const stars = Math.ceil(review.rating / 2)
    acc[stars] = (acc[stars] || 0) + 1
    return acc
  }, {} as Record<number, number>)

  const channelDistribution = reviews.reduce((acc, review) => {
    acc[review.channel] = (acc[review.channel] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2 space-y-6">
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
            <div className="space-y-6">
              <Skeleton className="h-48 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !property) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <Card>
          <CardContent className="p-8 text-center">
            <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Property Not Found</h2>
            <p className="text-muted-foreground mb-4">
              {error || 'The requested property could not be found.'}
            </p>
            <Button asChild>
              <Link to="/">Back to Dashboard</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">{property.name}</h1>
          <div className="flex items-center space-x-4 text-muted-foreground mt-2">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              {property.address}
            </div>
            <Badge variant="outline">{property.type}</Badge>
            <Badge variant={property.isActive ? "default" : "secondary"}>
              {property.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link to={`/property/${propertyId}/public`}>
              <Eye className="h-4 w-4 mr-2" />
              Public View
            </Link>
          </Button>
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Edit Property
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Content */}
        <div className="md:col-span-2 space-y-6">
          {/* Property Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Property Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{property.maxGuests}</p>
                    <p className="text-sm text-muted-foreground">Max Guests</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Bed className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{property.bedrooms}</p>
                    <p className="text-sm text-muted-foreground">Bedrooms</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Bath className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{property.bathrooms}</p>
                    <p className="text-sm text-muted-foreground">Bathrooms</p>
                  </div>
                </div>
              </div>
              {property.description && (
                <div className="mt-4">
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">{property.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Reviews Management */}
          <Tabs defaultValue="all" className="space-y-4">
            <TabsList>
              <TabsTrigger value="all">All Reviews ({reviews.length})</TabsTrigger>
              <TabsTrigger value="published">Published ({publishedReviews.length})</TabsTrigger>
              <TabsTrigger value="website">Website ({websiteReviews.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-4">
              {reviews.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No reviews yet</h3>
                    <p className="text-muted-foreground">
                      Reviews will appear here once guests start leaving feedback.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {reviews.slice(0, 5).map((review) => (
                    <Card key={review._id}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between space-x-4">
                          <div className="flex items-start space-x-3 flex-1">
                            <Avatar>
                              <AvatarFallback>
                                {review.guestName?.charAt(0) || 'G'}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-1">
                                <h4 className="font-medium">{review.guestName || 'Anonymous Guest'}</h4>
                                <Badge variant={getStatusBadgeVariant(review.status)} className="text-xs">
                                  {review.status}
                                </Badge>
                                {review.showOnWebsite && (
                                  <Badge variant="outline" className="text-xs">
                                    <Eye className="w-3 h-3 mr-1" />
                                    Website
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                                <div className="flex items-center space-x-1">
                                  <Calendar className="h-3 w-3" />
                                  <span>{formatDate(review.submittedAt)}</span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {review.channel}
                                </Badge>
                              </div>
                              <p className="text-sm leading-relaxed">
                                {review.publicReview || 'No review text available'}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Star className={`h-4 w-4 ${getRatingColor(review.rating)}`} fill="currentColor" />
                            <span className={`font-bold ${getRatingColor(review.rating)}`}>
                              {review.rating}/10
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  {reviews.length > 5 && (
                    <Card>
                      <CardContent className="p-4 text-center">
                        <p className="text-muted-foreground mb-2">
                          Showing 5 of {reviews.length} reviews
                        </p>
                        <Button variant="outline" size="sm">
                          View All Reviews
                        </Button>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="published">
              <div className="space-y-4">
                {publishedReviews.map((review) => (
                  <Card key={review._id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between space-x-4">
                        <div className="flex items-start space-x-3 flex-1">
                          <Avatar>
                            <AvatarFallback>
                              {review.guestName?.charAt(0) || 'G'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium mb-1">{review.guestName || 'Anonymous Guest'}</h4>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(review.submittedAt)}</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {review.channel}
                              </Badge>
                            </div>
                            <p className="text-sm leading-relaxed">
                              {review.publicReview || 'No review text available'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className={`h-4 w-4 ${getRatingColor(review.rating)}`} fill="currentColor" />
                          <span className={`font-bold ${getRatingColor(review.rating)}`}>
                            {review.rating}/10
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="website">
              <div className="space-y-4">
                {websiteReviews.map((review) => (
                  <Card key={review._id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between space-x-4">
                        <div className="flex items-start space-x-3 flex-1">
                          <Avatar>
                            <AvatarFallback>
                              {review.guestName?.charAt(0) || 'G'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium mb-1">{review.guestName || 'Anonymous Guest'}</h4>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                              <div className="flex items-center space-x-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(review.submittedAt)}</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {review.channel}
                              </Badge>
                            </div>
                            <p className="text-sm leading-relaxed">
                              {review.publicReview || 'No review text available'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className={`h-4 w-4 ${getRatingColor(review.rating)}`} fill="currentColor" />
                          <span className={`font-bold ${getRatingColor(review.rating)}`}>
                            {review.rating}/10
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Performance Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Performance</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className={`text-3xl font-bold ${getRatingColor(averageRating)}`}>
                  {averageRating > 0 ? averageRating.toFixed(1) : '0.0'}
                </div>
                <div className="flex justify-center mt-1">
                  {'⭐'.repeat(Math.round(averageRating / 2))}
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Based on {publishedReviews.length} reviews
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Published Rate</span>
                  <span>{reviews.length > 0 ? Math.round((publishedReviews.length / reviews.length) * 100) : 0}%</span>
                </div>
                <Progress value={reviews.length > 0 ? (publishedReviews.length / reviews.length) * 100 : 0} />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Website Visibility</span>
                  <span>{reviews.length > 0 ? Math.round((websiteReviews.length / reviews.length) * 100) : 0}%</span>
                </div>
                <Progress value={reviews.length > 0 ? (websiteReviews.length / reviews.length) * 100 : 0} />
              </div>
            </CardContent>
          </Card>

          {/* Channel Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Review Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(channelDistribution).map(([channel, count]) => (
                  <div key={channel} className="flex items-center justify-between">
                    <span className="text-sm">{channel}</span>
                    <div className="flex items-center space-x-2">
                      <Progress 
                        value={(count / reviews.length) * 100} 
                        className="w-16 h-2"
                      />
                      <span className="text-sm font-medium w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link to={`/property/${propertyId}/public`}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Public Page
                </Link>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Edit className="h-4 w-4 mr-2" />
                Edit Property
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default PropertyDetail
