.public-reviews {
  min-height: 100vh;
  background-color: var(--color-bg-primary);
}

.public-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.public-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: var(--color-text-secondary);
}

/* Hero Section - Flex Living Style */
.property-hero {
  position: relative;
  height: 500px;
  overflow: hidden;
  background: var(--color-primary);
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(50, 57, 39, 0.3) 0%, rgba(50, 57, 39, 0.8) 100%);
}

.hero-content {
  position: relative;
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding-bottom: 3rem;
  color: var(--color-white);
}

.property-title {
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.property-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 1.125rem;
  opacity: 0.95;
}

.property-features {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  opacity: 0.9;
}

.property-rating {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: var(--color-accent);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  font-weight: 600;
}

.rating-value {
  font-size: 1.25rem;
}

.rating-count {
  font-weight: 400;
  opacity: 0.8;
}

/* Reviews Section */
.reviews-section {
  padding: 4rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.section-subtitle {
  color: var(--color-text-secondary);
}

/* Review Statistics */
.review-stats {
  margin: 2rem 0;
  padding: 2rem;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.stats-overview {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 3rem;
  align-items: start;
}

.overall-rating {
  text-align: center;
  padding: 1rem;
}

.rating-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-primary);
  line-height: 1;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  margin: 0.5rem 0;
  color: #fbbf24;
}

.rating-text {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.category-ratings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.category-item {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 1rem;
  align-items: center;
}

.category-name {
  font-weight: 500;
  color: var(--color-text-primary);
  text-transform: capitalize;
}

.category-bar {
  height: 8px;
  background: var(--color-bg-secondary);
  border-radius: 4px;
  overflow: hidden;
}

.category-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width var(--transition-normal);
}

.category-score {
  font-weight: 600;
  color: var(--color-primary);
  min-width: 2rem;
  text-align: right;
}

.reviews-grid {
  display: grid;
  gap: 1.5rem;
}

.review-card-public {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.review-card-public:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.review-header-public {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.reviewer-info {
  display: flex;
  gap: 1rem;
}

.reviewer-avatar {
  width: 48px;
  height: 48px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
}

.reviewer-details h4 {
  color: var(--color-primary);
  margin-bottom: 0.25rem;
}

.review-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
}

.review-rating-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  background: var(--color-accent);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  font-weight: 600;
}

.review-text-public {
  color: var(--color-text-primary);
  line-height: 1.7;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.management-response {
  background: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
  border-left: 3px solid var(--color-accent);
}

.management-response h5 {
  color: var(--color-primary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.management-response p {
  color: var(--color-text-primary);
  font-size: 0.875rem;
  line-height: 1.6;
}

.review-categories-public {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.category-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
}

.category-name {
  color: var(--color-text-secondary);
  text-transform: capitalize;
}

.category-score {
  color: var(--color-primary);
  font-weight: 600;
}

.review-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-bg-secondary);
}

.review-channel {
  display: flex;
  align-items: center;
}

.channel-badge {
  padding: 0.25rem 0.75rem;
  background: var(--color-primary);
  color: white;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.helpful-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--color-text-muted);
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.helpful-btn:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-text-secondary);
  color: var(--color-text-primary);
}

.no-reviews {
  text-align: center;
  padding: 3rem;
  background: var(--color-white);
  border-radius: var(--radius-lg);
  color: var(--color-text-secondary);
}

/* CTA Section */
.cta-section {
  background: var(--color-primary);
  padding: 4rem 0;
  color: var(--color-white);
}

.cta-content {
  text-align: center;
}

.cta-content h3 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.cta-content p {
  margin-bottom: 2rem;
  opacity: 0.9;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

@media (max-width: 768px) {
  .property-title {
    font-size: 2rem;
  }

  .property-features {
    flex-direction: column;
    gap: 0.75rem;
  }

  .review-header-public {
    flex-direction: column;
    gap: 1rem;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .category-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: center;
  }

  .review-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
