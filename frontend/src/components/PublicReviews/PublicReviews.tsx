"use client"

import React, { useState, useEffect } from "react"
import { useParams } from "react-router-dom"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { Separator } from "@/components/ui/separator"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Star, 
  Calendar, 
  Filter,
  SortAsc,
  SortDesc,
  Building2,
  MapPin,
  Users,
  Bed,
  Bath
} from "lucide-react"
import { reviewsApi, propertiesApi, type Review, type Property } from "../../services/api"

const PublicReviews: React.FC = () => {
  const { propertyId } = useParams<{ propertyId: string }>()
  const [property, setProperty] = useState<Property | null>(null)
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'date' | 'rating'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filterRating, setFilterRating] = useState<string>('')

  useEffect(() => {
    const loadData = async () => {
      if (!propertyId) return

      try {
        setLoading(true)
        setError(null)

        const [propertyData, reviewsData] = await Promise.all([
          propertiesApi.getPropertyById(propertyId),
          reviewsApi.getReviews({ 
            listingId: propertyId, 
            showOnWebsite: true,
            status: 'published'
          })
        ])

        setProperty(propertyData)
        setReviews(reviewsData)
      } catch (err) {
        console.error('Error loading public reviews:', err)
        setError(err instanceof Error ? err.message : 'Failed to load reviews')
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [propertyId])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return "text-green-600"
    if (rating >= 6) return "text-yellow-600"
    return "text-red-600"
  }

  const filteredAndSortedReviews = reviews
    .filter(review => {
      if (!filterRating) return true
      const [min, max] = filterRating.split('-').map(Number)
      return review.rating >= min && review.rating <= max
    })
    .sort((a, b) => {
      if (sortBy === 'date') {
        const dateA = new Date(a.submittedAt).getTime()
        const dateB = new Date(b.submittedAt).getTime()
        return sortOrder === 'desc' ? dateB - dateA : dateA - dateB
      } else {
        return sortOrder === 'desc' ? b.rating - a.rating : a.rating - b.rating
      }
    })

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0

  const ratingDistribution = reviews.reduce((acc, review) => {
    const stars = Math.ceil(review.rating / 2)
    acc[stars] = (acc[stars] || 0) + 1
    return acc
  }, {} as Record<number, number>)

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-16 w-full" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error || !property) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-xl font-semibold mb-2">Property Not Found</h2>
            <p className="text-muted-foreground">
              {error || 'The requested property could not be found.'}
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Property Header */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center text-2xl">
                🏠
              </div>
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold mb-2">{property.name}</h1>
              <div className="flex items-center space-x-4 text-muted-foreground mb-3">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {property.address}
                </div>
                <Badge variant="outline">{property.type}</Badge>
              </div>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  {property.maxGuests} guests
                </div>
                <div className="flex items-center">
                  <Bed className="h-4 w-4 mr-1" />
                  {property.bedrooms} bedrooms
                </div>
                <div className="flex items-center">
                  <Bath className="h-4 w-4 mr-1" />
                  {property.bathrooms} bathrooms
                </div>
              </div>
            </div>
            {averageRating > 0 && (
              <div className="text-right">
                <div className="flex items-center space-x-1 mb-1">
                  <Star className={`h-5 w-5 ${getRatingColor(averageRating)}`} fill="currentColor" />
                  <span className={`text-xl font-bold ${getRatingColor(averageRating)}`}>
                    {averageRating.toFixed(1)}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  {reviews.length} review{reviews.length !== 1 ? 's' : ''}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Reviews Section */}
      <div className="space-y-6">
        {/* Filters and Sorting */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Guest Reviews</CardTitle>
                <CardDescription>
                  {filteredAndSortedReviews.length} of {reviews.length} reviews
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Select value={filterRating} onValueChange={setFilterRating}>
                  <SelectTrigger className="w-40">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="All ratings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All ratings</SelectItem>
                    <SelectItem value="8-10">8-10 stars</SelectItem>
                    <SelectItem value="6-8">6-8 stars</SelectItem>
                    <SelectItem value="4-6">4-6 stars</SelectItem>
                    <SelectItem value="1-4">1-4 stars</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
                  const [newSortBy, newSortOrder] = value.split('-') as ['date' | 'rating', 'asc' | 'desc']
                  setSortBy(newSortBy)
                  setSortOrder(newSortOrder)
                }}>
                  <SelectTrigger className="w-40">
                    {sortOrder === 'desc' ? <SortDesc className="h-4 w-4 mr-2" /> : <SortAsc className="h-4 w-4 mr-2" />}
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest first</SelectItem>
                    <SelectItem value="date-asc">Oldest first</SelectItem>
                    <SelectItem value="rating-desc">Highest rated</SelectItem>
                    <SelectItem value="rating-asc">Lowest rated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Reviews List */}
        {filteredAndSortedReviews.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Star className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium mb-2">No reviews found</h3>
              <p className="text-muted-foreground">
                {filterRating ? 'No reviews match your filter criteria.' : 'This property has no public reviews yet.'}
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedReviews.map((review) => (
              <Card key={review._id}>
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <Avatar className="flex-shrink-0">
                      <AvatarFallback>
                        {review.guestName?.charAt(0) || 'G'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium">{review.guestName || 'Anonymous Guest'}</h4>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(review.submittedAt)}</span>
                            <Separator orientation="vertical" className="h-3" />
                            <Badge variant="outline" className="text-xs">
                              {review.channel}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className={`h-4 w-4 ${getRatingColor(review.rating)}`} fill="currentColor" />
                          <span className={`font-bold ${getRatingColor(review.rating)}`}>
                            {review.rating}/10
                          </span>
                        </div>
                      </div>
                      <p className="text-sm leading-relaxed">
                        {review.publicReview || 'No review text available'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default PublicReviews
