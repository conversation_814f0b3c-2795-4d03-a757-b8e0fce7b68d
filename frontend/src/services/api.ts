// API Base Configuration
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL ||
  process.env.REACT_APP_API_URL ||
  "http://localhost:8000";

// Types
export interface Review {
  _id: string;
  externalId: number;
  guestName: string;
  listingId: string;
  listingName: string;
  channel: string;
  rating: number;
  status: string;
  publicReview: string;
  privateReview?: string;
  submittedAt: string;
  showOnWebsite: boolean;
  helpfulCount?: number;
  reviewCategory?: ReviewCategory[];
}

export interface ReviewCategory {
  category: string;
  rating: number;
}

export interface Property {
  _id: string;
  externalId: string;
  name: string;
  address: string;
  type: string;
  bedrooms: number;
  bathrooms: number;
  maxGuests: number;
  imageUrl?: string;
  avgRating?: number;
  totalReviews?: number;
  isActive: boolean;
}

export interface ReviewStatistics {
  overview: {
    avgRating: number;
    totalReviews: number;
    publishedReviews: number;
    websiteReviews: number;
  };
  categoryBreakdown: Array<{
    _id: string;
    count: number;
    avgRating: number;
  }>;
  channelBreakdown: Array<{
    _id: string;
    count: number;
    avgRating: number;
  }>;
  monthlyTrend: Array<{
    _id: { year: number; month: number };
    count: number;
    avgRating: number;
  }>;
}

export interface ApiResponse<T> {
  status: "success" | "error";
  data?: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// HTTP Client
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  async get<T>(
    endpoint: string,
    params?: Record<string, any>
  ): Promise<ApiResponse<T>> {
    const searchParams = params ? new URLSearchParams(params).toString() : "";
    const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
    return this.request<T>(url);
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "DELETE",
    });
  }
}

// Initialize API client
const apiClient = new ApiClient(API_BASE_URL);

// Reviews API
export const reviewsApi = {
  async getReviews(filters?: Record<string, any>): Promise<Review[]> {
    const response = await apiClient.get<Review[]>("/api/reviews", filters);
    return response.data || [];
  },

  async getReviewById(id: string): Promise<Review> {
    const response = await apiClient.get<Review>(`/api/reviews/${id}`);
    if (!response.data) throw new Error("Review not found");
    return response.data;
  },

  async updateReview(id: string, updates: Partial<Review>): Promise<Review> {
    const response = await apiClient.put<Review>(`/api/reviews/${id}`, updates);
    if (!response.data) throw new Error("Failed to update review");
    return response.data;
  },

  async getStatistics(
    filters?: Record<string, any>
  ): Promise<ReviewStatistics> {
    const response = await apiClient.get<ReviewStatistics>(
      "/api/reviews/statistics",
      filters
    );
    if (!response.data) throw new Error("Failed to get statistics");
    return response.data;
  },

  async markHelpful(id: string, helpful: boolean): Promise<Review> {
    const response = await apiClient.post<Review>(
      `/api/reviews/${id}/helpful`,
      { helpful }
    );
    if (!response.data) throw new Error("Failed to mark review as helpful");
    return response.data;
  },

  async exportReviews(
    format: "json" | "csv" = "json",
    filters?: Record<string, any>
  ): Promise<Blob> {
    const params = { ...filters, format };
    const response = await fetch(
      `${API_BASE_URL}/api/reviews/export?${new URLSearchParams(params)}`
    );
    return response.blob();
  },
};

// Properties API
export const propertiesApi = {
  async getProperties(filters?: Record<string, any>): Promise<Property[]> {
    const response = await apiClient.get<Property[]>(
      "/api/properties",
      filters
    );
    return response.data || [];
  },

  async getPropertyById(id: string): Promise<Property> {
    const response = await apiClient.get<Property>(`/api/properties/${id}`);
    if (!response.data) throw new Error("Property not found");
    return response.data;
  },

  async updateProperty(
    id: string,
    updates: Partial<Property>
  ): Promise<Property> {
    const response = await apiClient.put<Property>(
      `/api/properties/${id}`,
      updates
    );
    if (!response.data) throw new Error("Failed to update property");
    return response.data;
  },
};

// Hostaway API
export const hostawayApi = {
  async getReviews(useApi: boolean = false): Promise<any> {
    const response = await apiClient.get("/api/hostaway/reviews", { useApi });
    return response;
  },

  async syncData(): Promise<any> {
    const response = await apiClient.post("/api/hostaway/sync");
    return response;
  },

  async getReviewStatistics(): Promise<ReviewStatistics> {
    // For now, use the general reviews statistics endpoint
    const response = await apiClient.get<ReviewStatistics>(
      "/api/reviews/statistics"
    );
    if (!response.data) throw new Error("Failed to get review statistics");
    return response.data;
  },
};

// Google Places API
export const googleApi = {
  async searchPlaces(query: string): Promise<any> {
    const response = await apiClient.get("/api/google/place-search", { query });
    return response;
  },

  async getReviews(placeId: string): Promise<any> {
    const response = await apiClient.get("/api/google/reviews", { placeId });
    return response;
  },

  async getApiStatus(): Promise<any> {
    const response = await apiClient.get("/api/google/status");
    return response;
  },

  async testConnection(): Promise<any> {
    const response = await apiClient.get("/api/google/test");
    return response;
  },
};

export default {
  reviews: reviewsApi,
  properties: propertiesApi,
  hostaway: hostawayApi,
  google: googleApi,
};
