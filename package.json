{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@testing-library/dom": "latest", "@testing-library/jest-dom": "latest", "@testing-library/react": "latest", "@vercel/analytics": "1.3.1", "autoprefixer": "^10.4.20", "axios": "latest", "chart.js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cors": "latest", "date-fns": "4.1.0", "dotenv": "latest", "embla-carousel-react": "8.5.1", "express": "latest", "geist": "^1.3.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "mongoose": "latest", "next": "14.2.16", "next-themes": "^0.4.6", "path": "latest", "react": "^18", "react-chartjs-2": "latest", "react-day-picker": "9.8.0", "react-dom": "^18", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "latest", "recharts": "2.15.4", "sonner": "^1.7.4", "swagger-ui-express": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "web-vitals": "latest", "yamljs": "latest", "zod": "3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.9", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tw-animate-css": "1.3.3", "typescript": "^5"}}